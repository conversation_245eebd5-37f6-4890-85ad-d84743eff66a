package com.bugtracker.model;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "issues")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Issue {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, nullable = false)
    private String identifier;

    @Column(nullable = false)
    private String title;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(nullable = false)
    private String type;

    @Column(nullable = false)
    private String severity;

    @Column(nullable = false)
    private String priority;

    @Column(nullable = false)
    private String status;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "assignee_id")
    @JsonIgnoreProperties({"roles", "password"})
    private User assignee;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "reporter_id", nullable = false)
    @JsonIgnoreProperties({"roles", "password"})
    private User reporter;

    @Column(name = "dev_completion_date")
    private LocalDateTime devCompletionDate;

    @Column(name = "qc_completion_date")
    private LocalDateTime qcCompletionDate;

    @Column(name = "completion_date")
    private LocalDateTime completionDate;

    @Column(name = "root_cause", columnDefinition = "TEXT")
    private String rootCause;

    @Column(name = "reopen_count", nullable = false)
    @Builder.Default
    private Integer reopenCount = 0;

    // Conditional fields based on issue type
    @Column(name = "steps_to_reproduce", columnDefinition = "TEXT")
    @JsonProperty("stepsToReproduce")
    private String stepsToReproduce;

    @Column(name = "expected_result", columnDefinition = "TEXT")
    @JsonProperty("expectedResult")
    private String expectedResult;

    @Column(name = "actual_result", columnDefinition = "TEXT")
    @JsonProperty("actualResult")
    private String actualResult;

    @Column(name = "business_justification", columnDefinition = "TEXT")
    @JsonProperty("businessJustification")
    private String businessJustification;

    @Column(name = "acceptance_criteria", columnDefinition = "TEXT")
    @JsonProperty("acceptanceCriteria")
    private String acceptanceCriteria;

    @Column(name = "user_story", columnDefinition = "TEXT")
    @JsonProperty("userStory")
    private String userStory;

    @Column(name = "definition_of_done", columnDefinition = "TEXT")
    @JsonProperty("definitionOfDone")
    private String definitionOfDone;

    @Column(name = "research_notes", columnDefinition = "TEXT")
    @JsonProperty("researchNotes")
    private String researchNotes;

    @Column(name = "technical_approach", columnDefinition = "TEXT")
    @JsonProperty("technicalApproach")
    private String technicalApproach;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id")
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "children", "attachments", "comments", "watchers"})
    private Issue parent;

    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "parent", "attachments", "comments", "watchers"})
    @Builder.Default
    private List<Issue> children = new ArrayList<>();

    @Column(name = "version_release")
    private String versionRelease;

    @Column(nullable = false)
    private String environment;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "module_id")
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "submodules", "issues"})
    private Module module;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "submodule_id")
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "module", "issues"})
    private Submodule submodule;

    @OneToMany(mappedBy = "issue", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "issue"})
    @Builder.Default
    private List<Attachment> attachments = new ArrayList<>();

    @OneToMany(mappedBy = "issue", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "issue"})
    @Builder.Default
    private List<Comment> comments = new ArrayList<>();

    @ManyToMany
    @JoinTable(
        name = "issue_watchers",
        joinColumns = @JoinColumn(name = "issue_id"),
        inverseJoinColumns = @JoinColumn(name = "user_id")
    )
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "roles", "password"})
    @Builder.Default
    private Set<User> watchers = new HashSet<>();

    @ManyToMany
    @JoinTable(
        name = "issue_labels",
        joinColumns = @JoinColumn(name = "issue_id"),
        inverseJoinColumns = @JoinColumn(name = "label_id")
    )
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "issues"})
    @Builder.Default
    private Set<Label> labels = new HashSet<>();

    @OneToMany(mappedBy = "issue", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "issue"})
    @Builder.Default
    private List<ChecklistItem> checklistItems = new ArrayList<>();

    @OneToOne(mappedBy = "issue", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "issue"})
    private SlaTracking slaTracking;

    @OneToMany(mappedBy = "issue", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "issue"})
    @Builder.Default
    private List<TimeTracking> timeTrackingEntries = new ArrayList<>();

    @OneToMany(mappedBy = "issue", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "issue"})
    @Builder.Default
    private List<EffortEstimation> effortEstimations = new ArrayList<>();

    @OneToMany(mappedBy = "issue", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "issue"})
    @Builder.Default
    private List<PerformanceMetric> performanceMetrics = new ArrayList<>();

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;


}
